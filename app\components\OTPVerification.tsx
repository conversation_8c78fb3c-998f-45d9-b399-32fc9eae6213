"use client";
import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useSimpleToast } from "@/app/components/ui/SimpleToaster";

interface OTPVerificationProps {
  email: string;
  firstName: string;
  onVerificationSuccess: (otp: string) => void;
  onBack: () => void;
}

export default function OTPVerification({
  email,
  firstName,
  onVerificationSuccess,
  onBack,
}: OTPVerificationProps) {
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState("");
  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes in seconds
  const [canResend, setCanResend] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const showToast = useSimpleToast();

  // Timer for OTP expiration
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [timeLeft]);

  // Resend cooldown timer
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(
        () => setResendCooldown(resendCooldown - 1),
        1000
      );
      return () => clearTimeout(timer);
    } else {
      setCanResend(true);
    }
  }, [resendCooldown]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return; // Prevent multiple characters
    if (!/^\d*$/.test(value)) return; // Only allow digits

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError("");

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when all fields are filled
    if (newOtp.every((digit) => digit !== "") && !isLoading) {
      handleVerifyOtp(newOtp.join(""));
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").replace(/\D/g, "");
    if (pastedData.length === 6) {
      const newOtp = pastedData.split("");
      setOtp(newOtp);
      setError("");
      handleVerifyOtp(pastedData);
    }
  };

  const handleVerifyOtp = async (otpCode: string) => {
    setIsLoading(true);
    setError("");

    try {
      const response = await fetch("/api/auth/verify-otp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, otp: otpCode }),
      });

      const data = await response.json();

      if (response.ok) {
        showToast({ message: "Email verified successfully!", type: "success" });
        onVerificationSuccess(otpCode);
      } else {
        setError(data.error || "Verification failed");
        setOtp(["", "", "", "", "", ""]);
        inputRefs.current[0]?.focus();
      }
    } catch (error) {
      setError("Network error. Please try again.");
      setOtp(["", "", "", "", "", ""]);
      inputRefs.current[0]?.focus();
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async () => {
    setIsResending(true);
    setError("");

    try {
      const response = await fetch("/api/auth/resend-otp", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, firstName }),
      });

      const data = await response.json();

      if (response.ok) {
        showToast({ message: "OTP sent successfully!", type: "success" });
        setTimeLeft(600); // Reset timer to 10 minutes
        setCanResend(false);
        setResendCooldown(60); // 1 minute cooldown
        setOtp(["", "", "", "", "", ""]);
        inputRefs.current[0]?.focus();
      } else {
        setError(data.error || "Failed to resend OTP");
      }
    } catch (error) {
      setError("Network error. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-semibold mb-2">Verify Your Email</h2>
        <p className="text-gray-600">
          We've sent a 6-digit code to{" "}
          <span className="font-medium text-gray-900">{email}</span>
        </p>
      </div>

      <div className="mb-6">
        <Label className="block text-sm font-medium mb-3">
          Enter verification code
        </Label>
        <div className="flex gap-2 justify-center" onPaste={handlePaste}>
          {otp.map((digit, index) => (
            <Input
              key={index}
              ref={(el) => {
                inputRefs.current[index] = el;
              }}
              type="text"
              inputMode="numeric"
              maxLength={1}
              value={digit}
              onChange={(e) => handleOtpChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              className="w-12 h-12 text-center text-lg font-semibold"
              disabled={isLoading}
            />
          ))}
        </div>
      </div>

      {error && (
        <div className="text-red-600 text-sm text-center mb-4">{error}</div>
      )}

      <div className="text-center mb-6">
        {timeLeft > 0 ? (
          <p className="text-sm text-gray-600">
            Code expires in{" "}
            <span className="font-medium text-gray-900">
              {formatTime(timeLeft)}
            </span>
          </p>
        ) : (
          <p className="text-sm text-red-600">Code has expired</p>
        )}
      </div>

      <div className="space-y-3">
        <Button
          onClick={() => handleVerifyOtp(otp.join(""))}
          disabled={otp.some((digit) => !digit) || isLoading}
          className="w-full"
        >
          {isLoading ? "Verifying..." : "Verify Email"}
        </Button>

        <div className="text-center">
          <span className="text-sm text-gray-600">
            Didn't receive the code?{" "}
          </span>
          <button
            onClick={handleResendOtp}
            disabled={!canResend || isResending || resendCooldown > 0}
            className="text-sm text-primary hover:underline disabled:text-gray-400 disabled:no-underline"
          >
            {isResending
              ? "Sending..."
              : resendCooldown > 0
              ? `Resend in ${resendCooldown}s`
              : "Resend Code"}
          </button>
        </div>

        <Button
          variant="outline"
          onClick={onBack}
          className="w-full"
          disabled={isLoading || isResending}
        >
          Back to Registration
        </Button>
      </div>
    </div>
  );
}
