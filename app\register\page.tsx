"use client";
import React, { useState, useEffect, useRef } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useRouter } from "next/navigation";
import Logo from "../assets/images/logo-white.svg";
import Image from "next/image";
import Link from "next/link";
import { useSimpleToast } from "../components/ui/SimpleToaster";
import OTPVerification from "../components/OTPVerification";

// Add this at the top for TypeScript
declare global {
  interface Window {
    grecaptcha?: any;
  }
}

const accountTypes = [
  { value: "advertiser", label: "Advertiser" },
  { value: "publisher", label: "Publisher" },
  { value: "agency", label: "Agency" },
];

const countries = [
  { value: "us", label: "United States" },
  { value: "uk", label: "United Kingdom" },
  { value: "in", label: "India" },
  // Add more countries as needed
];
type Country = {
  code: string;
  flag: string;
};

export default function Register() {
  const router = useRouter();
  const [step, setStep] = useState(1); // 1: Account Details, 2: OTP Verification, 3: Company Details
  // Step 1 fields
  const [accountType, setAccountType] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [phone, setPhone] = useState("");
  const [teams, setTeams] = useState("");
  const [linkedin, setLinkedin] = useState("");
  // Step 2 fields
  const [company, setCompany] = useState("");
  const [vat, setVat] = useState("");
  const [country, setCountry] = useState("");
  const [address, setAddress] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [zip, setZip] = useState("");
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [notRobot, setNotRobot] = useState(false);
  const [error, setError] = useState("");
  const [captchaToken, setCaptchaToken] = useState("");
  const [otp, setOtp] = useState("");
  const [isLoadingOtp, setIsLoadingOtp] = useState(false);
  const recaptchaRef = useRef<HTMLDivElement>(null);
  const showToast = useSimpleToast();

  //Added for the countrycode
  const [countries, setCountries] = useState<Country[]>([]);
  const [selectedCode, setSelectedCode] = useState("+44");
  const [flag, setFlag] = useState("");

  useEffect(() => {
    if (step === 2 && typeof window !== "undefined") {
      if (!window.grecaptcha) {
        const script = document.createElement("script");
        script.src = "https://www.google.com/recaptcha/api.js";
        script.async = true;
        script.defer = true;
        document.body.appendChild(script);
        script.onload = () => {
          function renderCaptcha() {
            if (
              window.grecaptcha &&
              typeof window.grecaptcha.render === "function" &&
              recaptchaRef.current
            ) {
              window.grecaptcha.render(recaptchaRef.current, {
                sitekey: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY!,
                callback: (token: string) => setCaptchaToken(token),
              });
            } else {
              setTimeout(renderCaptcha, 100);
            }
          }
          renderCaptcha();
        };
      } else if (window.grecaptcha && recaptchaRef.current) {
        window.grecaptcha.render(recaptchaRef.current, {
          sitekey: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
          callback: (token: string) => setCaptchaToken(token),
        });
      }
    }

    //Added for the country code
    const fetchCountries = async () => {
      const res = await fetch(
        "https://restcountries.com/v3.1/all?fields=flags,idd"
      );
      const data = await res.json();

      const list: Country[] = data
        .filter((c: any) => c.idd?.root && c.idd?.suffixes?.length > 0)
        .map((c: any) => ({
          code: `${c.idd.root}${c.idd.suffixes[0]}`,
          flag: c.flags.png,
        }));

      // Remove duplicates based on code
      const unique = list.filter(
        (c, index, self) => self.findIndex((x) => x.code === c.code) === index
      );

      setCountries(unique);

      // Set default to +44 if exists
      const defaultCountry = unique.find((c) => c.code === "+44") || unique[0];
      setSelectedCode(defaultCountry.code);
      setFlag(defaultCountry.flag);
    };

    fetchCountries();
    //End
  }, [step]);

  function validateStep1() {
    if (
      !accountType ||
      !firstName ||
      !lastName ||
      !email ||
      !username ||
      !password ||
      !confirmPassword
    ) {
      setError("Please fill all required fields.");
      return false;
    }
    if (password.length < 8) {
      setError("Password must be at least 8 characters long.");
      return false;
    }
    // Regex: min 8 chars, at least 1 uppercase, 1 number, 1 special char
    const passwordRegex =
      /^(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_\-+=\[\]{};':"\\|,.<>\/?]).{8,}$/;
    if (!passwordRegex.test(password)) {
      setError(
        "Password must contain at least 8 characters, one uppercase letter, one number, and one special character."
      );
      return false;
    }
    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return false;
    }
    setError("");
    return true;
  }

  function validateStep2() {
    if (!company || !country || !acceptTerms) {
      setError("Please fill all required fields and complete the captcha.");
      return false;
    }
    setError("");
    return true;
  }

  async function handleNext(e: React.FormEvent) {
    e.preventDefault();
    if (step === 1 && validateStep1()) {
      // Check email and username availability and send OTP
      try {
        setIsLoadingOtp(true);
        const params = new URLSearchParams({
          email,
          username,
        });
        const res = await fetch(
          `/api/auth/check-availability?${params.toString()}`
        );
        const data = await res.json();

        if (!data.emailAvailable) {
          setError("Email is already in use.");
          return;
        }
        if (!data.usernameAvailable) {
          setError("Username is already taken.");
          return;
        }

        // Send OTP email directly
        const otpRes = await fetch("/api/auth/send-otp", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email, firstName }),
        });

        const otpData = await otpRes.json();

        if (otpRes.ok) {
          showToast({ message: "OTP sent to your email!", type: "success" });
          setStep(2); // Go directly to OTP verification step
        } else {
          setError(otpData.error || "Failed to send OTP");
        }
      } catch (err) {
        setError("Failed to check availability or send OTP. Please try again.");
      } finally {
        setIsLoadingOtp(false);
      }
    }
  }

  function handleBack() {
    if (step === 2) {
      setStep(1); // Go back to account details from OTP verification
    } else if (step === 3) {
      setStep(2); // Go back to OTP verification from company details
    } else {
      setStep(1);
    }
  }

  function handleOTPVerificationSuccess(otpCode: string) {
    setOtp(otpCode);
    setStep(3); // Go to company details after successful OTP verification
  }

  function handleBackFromOTP() {
    setStep(1); // Go back to account details
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setError("");

    if (step === 3 && validateStep2()) {
      // Check email and username availability before proceeding
      try {
        const params = new URLSearchParams({
          company,
        });
        const res = await fetch(
          `/api/auth/check-availability?${params.toString()}`
        );
        const data = await res.json();

        if (!data.companyAvailable) {
          setError("Company name already exist.");
          return;
        }
        setStep(2);
      } catch (err) {
        setError("Failed to check availability. Please try again.");
      }

      const payload = {
        first_name: firstName,
        last_name: lastName,
        email: email,
        password: password,
        confirm_password: confirmPassword,
        account_type: accountType,
        phone: phone,
        teams: teams,
        linkedin: linkedin,
        company: company,
        vat: vat,
        country: country,
        state: state,
        city: city,
        zip: zip,
        address: address,
        accept_terms: acceptTerms ? "true" : "false",
        username: username,
        otp: otp, // Include OTP for verification
      };

      console.log("payload: ", payload);

      const res = await fetch("/api/auth/register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      const data = await res.json();
      if (data?.user?.id && data?.user?.status === true) {
        showToast({
          message: "Registration Successful! Please login to continue.",
          type: "success",
        });
        setTimeout(() => {
          router.push("/");
        }, 1500);
      } else {
        showToast({
          message: data.error || "Registration failed",
          type: "error",
        });
        setError(data.error || "Registration failed");
      }
    }
  }
  //Added  for countrycode
  const handleCountryCodeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selected = countries.find((c) => c.code === e.target.value);
    if (selected) {
      setSelectedCode(selected.code);
      setFlag(selected.flag);
    }
  };
  //End

  return (
    <div className="flex min-h-screen register-wrapper">
      {/* Left Panel */}
      <div className="left-panel hidden lg:flex flex-col justify-between bg-primary text-white w-full min-w-[360px] max-w-[630px] px-12 py-14  relative">
        <div>
          <div className="flex flex-col mb-28">
            <Image src={Logo} alt="logo" className="max-w-[200px]" />
          </div>
          <div className="mb-20 pl-6 border-l border-white">
            <h2 className="text-4xl font-[200] mb-2">Welcome!</h2>
            <p className="text-lg font-[200]">
              Let's Get Your Revenue Dashboard Set Up
            </p>
          </div>
          <div className="space-y-6">
            <div className="flex items-center space-x-4">
              <div
                className={`w-12 h-12 rounded-full flex items-center justify-center border font-[500] ${
                  step === 1
                    ? "bg-white text-black border-white"
                    : "bg-white bg-opacity-70 text-black border-white"
                }`}
              >
                01
              </div>
              <span className="text-lg">Account Details</span>
            </div>
            <div className="flex items-center space-x-4">
              <div
                className={`w-12 h-12 rounded-full flex items-center justify-center border font-[500] ${
                  step === 2
                    ? "bg-white text-black border-white"
                    : "bg-white bg-opacity-70 text-black border-white"
                }`}
              >
                02
              </div>
              <span className="text-lg">OTP Verification</span>
            </div>
            <div className="flex items-center space-x-4">
              <div
                className={`w-12 h-12 rounded-full flex items-center justify-center border font-[500] ${
                  step === 3
                    ? "bg-white text-black border-white"
                    : "bg-white bg-opacity-70 text-black border-white"
                }`}
              >
                03
              </div>
              <span className="text-lg">User/Company Details</span>
            </div>
          </div>
        </div>
      </div>
      {/* Right Panel */}
      <div className="w-full  flex items-center flex-col justify-center bg-white py-10 overflow-auto px-8">
        <div className="w-full max-w-[650px] flex-1">
          {step === 1 ? (
            <form onSubmit={handleNext}>
              <div className="mb-12">
                <div className="text-3xl mb-2 font-[600]">Account Details</div>
                <div className="text-sm text-gray-500">
                  Create Your Account to Access Revenue Insights
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="grid gap-2 md:col-span-2">
                  <Label className="gap-1" htmlFor="accountType">
                    Account Type<span className="text-red-700">*</span>
                  </Label>
                  <div>
                    <Select
                      value={accountType}
                      onValueChange={setAccountType}
                      required
                    >
                      <SelectTrigger id="accountType" className="w-full">
                        <SelectValue placeholder="Select account type" />
                      </SelectTrigger>
                      <SelectContent className="bg-white w-full">
                        {accountTypes.map((type) => (
                          <SelectItem
                            className="cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-cyan-600 data-[state=checked]:text-white focus:bg-gray-100"
                            key={type.value}
                            value={type.value}
                          >
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid gap-2 ">
                  <Label className="gap-1" htmlFor="firstName">
                    First Name<span className="text-red-700">*</span>
                  </Label>
                  <Input
                    id="firstName"
                    value={firstName}
                    onChange={(e) => setFirstName(e.target.value)}
                    placeholder="Enter your first name"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label className="gap-1" htmlFor="lastName">
                    Last Name<span className="text-red-700">*</span>
                  </Label>
                  <Input
                    id="lastName"
                    value={lastName}
                    onChange={(e) => setLastName(e.target.value)}
                    placeholder="Enter your last name"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label className="gap-1" htmlFor="email">
                    Email Address<span className="text-red-700">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label className="gap-1" htmlFor="username">
                    Username<span className="text-red-700">*</span>
                  </Label>
                  <Input
                    id="username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder="Choose a username"
                    required
                    autoComplete="false"
                  />
                </div>
                <div className="grid gap-2 md:col-span-2">
                  <Label className="gap-1" htmlFor="password">
                    Password<span className="text-red-700">*</span>
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Create a password"
                    required
                    autoComplete="false"
                  />
                  <span className="text-xs text-gray-500">
                    Password must be at least 8 characters long
                  </span>
                </div>
                <div className="grid gap-2 md:col-span-2">
                  <Label className="gap-1" htmlFor="confirmPassword">
                    Confirm Password<span className="text-red-700">*</span>
                  </Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm your password"
                    required
                  />
                </div>
                {/* <div className="grid gap-2">
                  <Label className="gap-1" htmlFor="phone">
                    Phone
                  </Label>
                  <Input
                    id="phone"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                    placeholder="Phone number"
                  />
                </div> */}

                <div className="grid gap-2">
                  <Label className="gap-1" htmlFor="phone">
                    Phone
                  </Label>
                  <div className="flex border overflow-hidden rounded-md">
                    <div className="flex items-center min-w-[110px]">
                      <div className="flex items-center px-2 bg-white">
                        {flag && (
                          <img
                            src={flag}
                            alt="flag"
                            className="w-5 h-4 mr-1 rounded-sm"
                          />
                        )}
                        <Select
                          value={selectedCode}
                          onValueChange={(val) => {
                            const selected = countries.find(
                              (c) => c.code === val
                            );
                            if (selected) {
                              setSelectedCode(selected.code);
                              setFlag(selected.flag);
                            }
                          }}
                        >
                          <SelectTrigger className="bg-white text-sm outline-none border-0 focus-visible:ring-0">
                            <SelectValue placeholder="Code" />
                          </SelectTrigger>
                          <SelectContent className="bg-white max-h-[250px]">
                            {countries.map((c) => (
                              <SelectItem
                                className="cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-cyan-600 data-[state=checked]:text-white focus:bg-gray-100"
                                key={c.code}
                                value={c.code}
                              >
                                {c.code}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <Input
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      placeholder="Phone number"
                      className="border-0 rounded-none border-l"
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label className="gap-1" htmlFor="teams">
                    Microsoft Teams username
                  </Label>
                  <Input
                    id="teams"
                    value={teams}
                    onChange={(e) => setTeams(e.target.value)}
                    placeholder="Enter your teams username"
                  />
                </div>
                <div className="grid gap-2 md:col-span-2">
                  <Label className="gap-1" htmlFor="linkedin">
                    LinkedIn Profile
                  </Label>
                  <Input
                    id="linkedin"
                    value={linkedin}
                    onChange={(e) => setLinkedin(e.target.value)}
                    placeholder="Enter your LinkedIn profile"
                  />
                </div>
              </div>
              {error && (
                <div className="text-red-500 text-sm mt-2">{error}</div>
              )}

              <div className="flex flex-col gap-2 mt-10">
                <Button
                  className="w-full text-white"
                  type="submit"
                  disabled={isLoadingOtp}
                >
                  {isLoadingOtp ? "Sending..." : "Email Verification"}
                </Button>
              </div>
            </form>
          ) : step === 2 ? (
            <OTPVerification
              email={email}
              firstName={firstName}
              onVerificationSuccess={handleOTPVerificationSuccess}
              onBack={handleBackFromOTP}
            />
          ) : (
            <form onSubmit={handleSubmit}>
              <div className="mb-12">
                <div className="text-3xl mb-2 font-[600]">
                  User/Company Details
                </div>
                <div className="text-sm text-gray-500">
                  Tell us about your organization
                </div>
              </div>
              <div className="grid">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="grid gap-2 md:col-span-2">
                    <Label className="gap-1" htmlFor="company">
                      Company<span className="text-red-700">*</span>
                    </Label>
                    <Input
                      id="company"
                      value={company}
                      onChange={(e) => setCompany(e.target.value)}
                      placeholder="Enter company name"
                      required
                    />
                  </div>
                  <div className="grid gap-2 md:col-span-2">
                    <Label htmlFor="vat">VAT Number</Label>
                    <Input
                      id="vat"
                      value={vat}
                      onChange={(e) => setVat(e.target.value)}
                      placeholder="Enter VAT number"
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label className="gap-1" htmlFor="country">
                      Country<span className="text-red-700">*</span>
                    </Label>
                    <Select value={country} onValueChange={setCountry} required>
                      <SelectTrigger id="country" className="w-full">
                        <SelectValue placeholder="Select your country" />
                      </SelectTrigger>
                      <SelectContent className="bg-white w-full">
                        {countries.map((c) => (
                          <SelectItem
                            className="cursor-pointer hover:bg-gray-100 data-[state=checked]:bg-cyan-600 data-[state=checked]:text-white focus:bg-gray-100"
                            key={c.code}
                            value={c.code}
                          >
                            {c.code}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="state">State / Province</Label>
                    <Input
                      id="state"
                      value={state}
                      onChange={(e) => setState(e.target.value)}
                      placeholder="Enter state or province"
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      placeholder="Enter city"
                    />
                  </div>

                  <div className="grid gap-2">
                    <Label htmlFor="zip">ZIP / Postcode</Label>
                    <Input
                      id="zip"
                      value={zip}
                      onChange={(e) => setZip(e.target.value)}
                      placeholder="Enter ZIP or postcode"
                    />
                  </div>
                  <div className="grid gap-2 md:col-span-2">
                    <Label htmlFor="address">Street Address</Label>
                    <Input
                      id="address"
                      value={address}
                      onChange={(e) => setAddress(e.target.value)}
                      placeholder="Enter street address"
                    />
                  </div>
                </div>
                <div className="flex items-center space-x-2 mt-6">
                  <Checkbox
                    className="accent-blue-600"
                    id="acceptTerms"
                    checked={acceptTerms}
                    onCheckedChange={(checked) => setAcceptTerms(!!checked)}
                  />
                  <Label htmlFor="acceptTerms" className="text-sm">
                    Accept terms and condition
                  </Label>
                </div>
                <div className="text-xs text-gray-500 ml-6">
                  By creating an account, you agree to our{" "}
                  <a href="#" className="text-primary hover:underline">
                    Terms of Service
                  </a>{" "}
                  and{" "}
                  <a href="#" className="text-primary hover:underline">
                    Privacy Policy
                  </a>
                  .
                </div>
                {/* <div ref={recaptchaRef} id="recaptcha-container" className="my-4" /> */}
                {error && (
                  <div className="text-red-500 text-sm mt-2">{error}</div>
                )}
              </div>
              <div className="flex flex-col gap-2 mt-10">
                <div className="flex gap-2 w-full">
                  <Button
                    className="text-white w-full"
                    type="submit"
                    disabled={!acceptTerms}
                  >
                    Create Account
                  </Button>
                </div>
              </div>
            </form>
          )}
        </div>
        <div className="text-sm text-center text-gray-500 mt-12">
          Already have an account?{" "}
          <Link href="/" className="text-primary font-medium hover:underline">
            Sign In
          </Link>
        </div>
      </div>
    </div>
  );
}
