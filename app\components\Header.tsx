"use client";
import React from "react";
import Link from "next/link";
import { useAuth } from "@/app/context/AuthContext";

import defaultProfileImg from "../assets/images/avatar.png";
import Image from "next/image";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter, usePathname } from "next/navigation";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Loader } from "@/components/ui/loader"; // Import your loader

// Helper to get meta value by key
function getMetaValue(metas: any[], key: string) {
  return metas?.find((meta) => meta.meta_key === key)?.meta_value || "";
}

export default function Header() {
  const router = useRouter();
  const pathname = usePathname();
  const { isLoggedIn, user, logout, loading } = useAuth();
  const [loggingOut, setLoggingOut] = React.useState(false); // Add state

  // Map pathnames to page titles
  const pageTitles: Record<string, string> = {
    "/dashboard": "Dashboard",
    "/manageSite": "Manage Sites",
    "/publishers": "Publishers",
    "/affiliates": "Affiliates",
    "/products": "Products",
    "/payments": "Payments",
    "/reports": "Reports",
    "/settings": "Settings",
    "/profile": "Profile Settings",
    // Add more as needed
  };
  const pageTitle =
    pathname && pathname.startsWith("/create")
      ? "Create Site"
      : pathname && pathname.startsWith("/edit")
      ? "Edit Site"
      : pageTitles[pathname] ||
        (pathname
          ? pathname
              .replace(/^\//, "")
              .replace(/-/g, " ")
              .replace(/\b\w/g, (c) => c.toUpperCase())
          : "Dashboard");

  // Get first and last name from user.metas
  const firstName = getMetaValue(user?.metas, "first_name");
  const lastName = getMetaValue(user?.metas, "last_name");
  const displayName = `${firstName} ${lastName}`.trim() || "Wilson Workman";
  const roleName = user?.role?.name || "User";

  if (loading || !isLoggedIn) return null;

  const handleLogout = async () => {
    setLoggingOut(true); // Show loader
    try {
      await fetch("/api/auth/logout", { method: "POST" });
      await logout();
      router.push("/");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <header className="bg-white border-b border-gray-300 z-50 w-full">
      <nav className="px-6 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 border data-[orientation=vertical]:h-4"
            />
            <h1 className="text-xl font-semibold">{pageTitle}</h1>
          </div>

          <div className="flex space-x-4">
            {loggingOut && (
              <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-white bg-opacity-70">
                <Loader />
              </div>
            )}
            <DropdownMenu>
              <DropdownMenuTrigger
                className="text-left flex space-x-3 text-gray-600 hover:text-gray-900 focus:outline-none"
                aria-label="User menu"
              >
                <Image
                  src={defaultProfileImg}
                  alt="User Avatar"
                  width={40}
                  height={40}
                  className="rounded-lg"
                />

                <div className="gap-0 flex-col hidden sm:flex">
                  <h5 className="font-[600]">{displayName}</h5>
                  <span className="text-sm">{roleName}</span>
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-48 bg-white">
                <DropdownMenuItem asChild>
                  <Link href="#" className="dropdown-item-hover">
                    Profile Settings
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="dropdown-item-hover"
                  disabled={loggingOut} // Disable button while logging out
                >
                  Logout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </nav>
    </header>
  );
}
