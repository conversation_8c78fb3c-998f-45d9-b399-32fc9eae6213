import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';
import dummyImage from '../../../../assets/images/dummy/dummy-article.webp';
import { uploadToSpaces } from '@/lib/do-spaces';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });


export async function POST(req: NextRequest) {
	try {
		const { imagePrompt, title, niche } = await req.json();
		if (!imagePrompt || !title) {
			return NextResponse.json({ error: 'imagePrompt and title are required' }, { status: 400 });
		}

		// Return dummy image in development
		if (process.env.APP_ENV !== 'production') {
			try {
				const generatedDir = path.join(process.cwd(), 'public', 'generated');
				const files = await fs.readdir(generatedDir);
				const imageFiles = files.filter(f => /\.(webp|png|jpg|jpeg)$/i.test(f));
				if (imageFiles.length > 0) {
					const randomFile = imageFiles[Math.floor(Math.random() * imageFiles.length)];
					return NextResponse.json({ imageUrl: `${process.env.API_URL}/generated/${randomFile}` }, { status: 200 });
				}
			} catch (e) {
				// ignore error, fallback to dummy
			}
			return NextResponse.json({ imageUrl: dummyImage.src }, { status: 200 });
		}

		const prompt = imagePrompt || `${title}, ${niche}, high definition, 1024x1024`;
		const imageResponse = await openai.images.generate({
			model: "dall-e-3",
			prompt,
			n: 1,
			size: '1792x1024',
			quality: "hd",
			style: "natural"
		});
		const remoteUrl = imageResponse.data?.[0]?.url;
		if (!remoteUrl) {
			return NextResponse.json({ error: 'No image generated' }, { status: 500 });
		}
		const res = await fetch(remoteUrl);
		const arrayBuffer = await res.arrayBuffer();
		const buffer = Buffer.from(arrayBuffer);
		const slug = title
			.toLowerCase()
			.replace(/[^a-z0-9]+/g, '-')
			.replace(/^-+|-+$/g, '')
			.substring(0, 50);
		const filename = `${slug}-${uuidv4().slice(0, 8)}.webp`;

		let quality = 90;
		let compressedBuffer = await sharp(buffer)
			.resize({ width: 1024, height: 1024, fit: 'cover' })
			.webp({ quality })
			.toBuffer();
		while (compressedBuffer.length > 1024 * 1024 && quality > 50) {
			quality -= 10;
			compressedBuffer = await sharp(buffer)
				.resize({ width: 1024, height: 1024, fit: 'cover' })
				.webp({ quality })
				.toBuffer();
		}

		const imageUrl = await uploadToSpaces(compressedBuffer, filename, "generated", "image/webp");
		return NextResponse.json({ imageUrl: imageUrl }, { status: 200 });
	} catch (error) {
		console.error('Error generating image:', error);
		return NextResponse.json({ error: 'Failed to generate image' }, { status: 500 });
	}
}
