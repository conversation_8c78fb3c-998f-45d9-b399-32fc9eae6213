import crypto from 'crypto';

// Generate a 6-digit OTP
export function generateOTP(): string {
  return crypto.randomInt(100000, 999999).toString();
}

// Generate OTP expiration time (10 minutes from now)
export function generateOTPExpiration(): Date {
  const now = new Date();
  return new Date(now.getTime() + 10 * 60 * 1000); // 10 minutes
}

// Check if OTP is expired
export function isOTPExpired(expiresAt: Date): boolean {
  return new Date() > expiresAt;
}

// Validate OTP format (6 digits)
export function isValidOTPFormat(otp: string): boolean {
  return /^\d{6}$/.test(otp);
}

// Hash OTP for secure storage (optional, for extra security)
export function hashOTP(otp: string): string {
  return crypto.createHash('sha256').update(otp).digest('hex');
}

// Verify hashed OTP
export function verifyHashedOTP(otp: string, hashedOTP: string): boolean {
  return hashOTP(otp) === hashedOTP;
}
