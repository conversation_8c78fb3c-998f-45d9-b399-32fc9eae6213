import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { generateOTP, generateOTPExpiration } from "@/lib/otp";
import { sendOTPEmail } from "@/lib/email";

export async function POST(req: NextRequest) {
  try {
    const { email, firstName } = await req.json();

    if (!email || !firstName) {
      return NextResponse.json(
        { error: "<PERSON>ail and firstName are required" },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    console.log("existingUser: ", existingUser);

    if (existingUser && existingUser.email_verified) {
      return NextResponse.json(
        { error: "Email is already verified" },
        { status: 409 }
      );
    }

    // Generate OTP
    const otpCode = generateOTP();
    const expiresAt = generateOTPExpiration();

    // Delete any existing OTPs for this email
    await prisma.oTP.deleteMany({
      where: {
        email,
        purpose: "email_verification",
      },
    });

    // console.log("prisma.oTP: ", prisma.oTP);

    // Create new OTP record
    const otpRecord = await prisma.oTP.create({
      data: {
        email,
        otp_code: otpCode,
        purpose: "email_verification",
        expires_at: expiresAt,
        user_id: existingUser?.id || "", // Will be updated when user is created
      },
    });

    // Send OTP email
    const emailResult = await sendOTPEmail(email, otpCode, firstName);

    if (!emailResult.success) {
      // Delete the OTP record if email sending failed
      await prisma.oTP.delete({
        where: { id: otpRecord.id },
      });

      return NextResponse.json(
        { error: "Failed to send OTP email" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "OTP sent successfully",
      expiresAt: expiresAt.toISOString(),
    });
  } catch (error) {
    console.error("Error sending OTP:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
