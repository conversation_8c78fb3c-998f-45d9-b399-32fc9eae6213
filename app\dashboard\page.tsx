"use client";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { BarChart2, Users, Hourglass, Package, HandCoins } from "lucide-react";
import React from "react";

const summary = [
  {
    label: "Net Revenue",
    value: "$0",
    icon: <BarChart2 className="w-6 h-6 text-cyan-600" />,
    growth: "+0%",
    growthType: "up",
  },
  {
    label: "Active Publishers",
    value: "0",
    icon: <Users className="w-6 h-6 text-orange-500" />,
    growth: "+0%",
    growthType: "up",
  },
  {
    label: "Pending Payouts",
    value: "0",
    icon: <Hourglass className="w-6 h-6 text-yellow-500" />,
    growth: "+0%",
    growthType: "up",
  },
  {
    label: "Products",
    value: "0",
    icon: <Package className="w-6 h-6 text-purple-600" />,
    growth: "+0%",
    growthType: "up",
  },
  {
    label: "Agent Shares",
    value: "$0",
    icon: <HandCoins className="w-6 h-6 text-green-600" />,
    growth: "No change",
    growthType: "up",
  },
];

const publisherRankings = [
  {
    publisher: "techreview.com",
    revenue: "$0",
    growth: "+0%",
    growthType: "up",
  },
  {
    publisher: "gadgetinsights.com",
    revenue: "$0",
    growth: "+0%",
    growthType: "up",
  },
  {
    publisher: "techtrendshub.com",
    revenue: "$0",
    growth: "+0%",
    growthType: "up",
  },
  {
    publisher: "digitalgearreview.com",
    revenue: "$0",
    growth: "+0%",
    growthType: "up",
  },
  {
    publisher: "innovativetechdigest.com",
    revenue: "$0",
    growth: "+0%",
    growthType: "up",
  },
];

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <div className="flex gap-4 flex-col-reverse justify-between lg:flex-row lg:items-center">
        {/* Overview Title */}
        <h2 className="text-2xl font-bold">Overview</h2>
        {/* Time Range Tabs */}
        <Tabs defaultValue="1y" className="w-full lg:w-auto">
          <TabsList className="w-full lg:w-auto">
            <TabsTrigger value="today" className="w-[80px]">
              Today
            </TabsTrigger>
            <TabsTrigger value="7d" className="w-[80px]">
              7 Days
            </TabsTrigger>
            <TabsTrigger value="30d" className="w-[80px]">
              30 Days
            </TabsTrigger>
            <TabsTrigger value="1y" className="w-[80px]">
              1 Year
            </TabsTrigger>
            <TabsTrigger value="custom" className="w-[80px]">
              Custom
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
        {summary.map((item) => (
          <Card key={item.label} className="flex flex-col gap-2 p-4 bg-white">
            <div className="flex items-center gap-2">
              {item.icon}
              <span className="font-medium text-gray-500 text-sm">
                {item.label}
              </span>
            </div>
            <div className="flex items-end justify-between mt-2">
              <span className="text-2xl font-bold">{item.value}</span>
              <span
                className={`text-xs font-semibold ml-2 flex items-center ${
                  item.growthType === "up"
                    ? "text-emerald-600"
                    : item.growthType === "down"
                    ? "text-red-700"
                    : "text-gray-400"
                }`}
              >
                {item.growthType === "up" && (
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M5 10l7-7m0 0l7 7m-7-7v18"
                    />
                  </svg>
                )}
                {item.growthType === "down" && (
                  <svg
                    className="w-3 h-3 mr-1"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M19 14l-7 7m0 0l-7-7m7 7V3"
                    />
                  </svg>
                )}
                {item.growth}
              </span>
            </div>
          </Card>
        ))}
      </div>

      {/* Clicks and Revenue Analysis */}
      <Card className="bg-white p-0 gap-0">
        <h3 className="text-lg font-semibold border-b m-0 px-6 py-4">
          Clicks and Revenue Analysis
        </h3>
        {/* Chart Placeholder */}
        <div className="p-6 w-full h-72 flex items-center justify-center">
          <span className="text-gray-400">[Chart Placeholder]</span>
        </div>
      </Card>
      {/* Publisher Rankings Table */}
      <Card className="bg-white p-0 gap-0">
        <h3 className="text-lg font-semibold border-b m-0 px-6 py-4">
          Publisher Rankings
        </h3>
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead>
              <tr className="text-left text-gray-500 border-b-2 bg-gray-100">
                <th className="py-3 px-4 font-medium">Publisher</th>
                <th className="py-3 px-4 font-medium text-right">Revenue</th>
                <th className="py-3 px-4 font-medium text-right">Growth</th>
              </tr>
            </thead>
            <tbody>
              {publisherRankings.map((row) => (
                <tr key={row.publisher} className="border-b last:border-0">
                  <td className="py-3 px-4 whitespace-nowrap">
                    {row.publisher}
                  </td>
                  <td className="py-3 px-4 font-semibold text-right">
                    {row.revenue}
                  </td>
                  <td
                    className={`py-3 px-4 font-semibold flex items-center justify-end ${
                      row.growthType === "up"
                        ? "text-emerald-600"
                        : row.growthType === "down"
                        ? "text-red-700"
                        : "text-gray-400"
                    }`}
                  >
                    {row.growthType === "up" && (
                      <svg
                        className="w-3 h-3 mr-1"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M5 10l7-7m0 0l7 7m-7-7v18"
                        />
                      </svg>
                    )}
                    {row.growthType === "down" && (
                      <svg
                        className="w-3 h-3 mr-1"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          d="M19 14l-7 7m0 0l-7-7m7 7V3"
                        />
                      </svg>
                    )}
                    {row.growth}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
}
