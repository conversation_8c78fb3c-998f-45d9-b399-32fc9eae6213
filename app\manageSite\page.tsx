"use client";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Pencil, Plus, Trash } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Loader } from "@/components/ui/loader";

type SiteMeta = {
  meta_key: string;
  meta_value: string;
};

type Site = {
  id: string;
  domain: string;
  site_name: string;
  status: boolean;
  site_meta: SiteMeta[];
};

async function deleteSite(domain: string) {
  const res = await fetch(`/api/site/delete`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ domain }),
  });
  if (!res.ok) throw new Error("Failed to delete site");
}

export default function Page() {
  const router = useRouter();
  const handleEdit = (domain: string) => {
    // Implement edit logic or navigation
    alert(`Edit ${domain}`);
  };

  const [sites, setSites] = useState<Site[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [siteToDelete, setSiteToDelete] = useState<string | null>(null);
  const [navigating, setNavigating] = useState(false);

  const handleDelete = (domain: string) => {
    setSiteToDelete(domain);
    setDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (siteToDelete) {
      // Call your delete API here
      await deleteSite(siteToDelete);
      setDeleteModalOpen(false);
      setSiteToDelete(null);
      // Optionally refresh the site list
    }
  };

  const cancelDelete = () => {
    setDeleteModalOpen(false);
    setSiteToDelete(null);
  };

  useEffect(() => {
    const fetchSites = async () => {
      try {
        const res = await fetch("/api/site/get-all");
        if (!res.ok) throw new Error("Failed to fetch sites");
        const data = await res.json();
        console.log("data", data);
        setSites(data);
      } catch (err: any) {
        setError(err.message || "Unknown error");
      } finally {
        setLoading(false);
      }
    };
    fetchSites();
  }, []);

  const handleClick = () => {
    setNavigating(true);
    router.push("/create");

    // Reset navigating state after a timeout in case navigation fails
    setTimeout(() => {
      setNavigating(false);
    }, 5000);
  };

  return (
    <div>
      {navigating && <Loader fullScreen text="Loading create page..." />}
      <div className="flex justify-between mb-6 w-full">
        <Input
          className="bg-white mr-6 max-w-[300px]"
          type="search"
          placeholder="Search Site"
        />
        <Button
          className="text-white"
          onClick={handleClick}
          disabled={navigating}
        >
          <Plus className="!w-5 !h-5" />
          Create New Site
        </Button>
      </div>
      <Card className="bg-white p-0 gap-0 w-full">
        <h3 className="text-lg font-semibold border-b m-0 px-6 py-4">
          Site Inventory
        </h3>
        <div className="overflow-x-auto">
          {loading ? (
            <div className="p-6 text-center text-gray-500">Loading...</div>
          ) : error ? (
            <div className="p-6 text-center text-red-500">{error}</div>
          ) : sites.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              Site not available, please create a site to view
            </div>
          ) : (
            <table className="min-w-full text-sm">
              <thead>
                <tr className="text-left text-gray-500 border-b-2 bg-gray-100 whitespace-nowrap">
                  <th className="py-3 px-4 font-medium">Site Name</th>
                  <th className="py-3 px-4 font-medium">Domain</th>
                  <th className="py-3 px-4 font-medium">Ad Slot Type</th>
                  <th className="py-3 px-4 font-medium">Slot Size</th>
                  <th className="py-3 px-4 font-medium">Impressions</th>
                  <th className="py-3 px-4 font-medium">Status</th>
                  <th className="py-3 px-4 font-medium text-center">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sites.map((site) => (
                  <tr key={site.id} className="border-b last:border-0">
                    <td className="py-3 px-4 whitespace-nowrap">
                      {site.site_name}
                    </td>
                    <td className="py-3 px-4 whitespace-nowrap">
                      <a
                        href={`https://${site.domain}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-cyan-700 hover:underline"
                      >
                        {site.domain}
                      </a>
                    </td>
                    <td className="py-3 px-4 whitespace-nowrap">-</td>
                    <td className="py-3 px-4 whitespace-nowrap">-</td>
                    <td className="py-3 px-4 whitespace-nowrap">-</td>
                    <td className="py-3 px-4">
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-semibold ${
                          site.status
                            ? "bg-green-50 text-green-600"
                            : "bg-gray-100 text-gray-500"
                        }`}
                      >
                        {site.status ? "Active" : "Inactive"}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex justify-center gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEdit(site.domain)}
                        >
                          <Pencil className="w-4 h-4 text-cyan-600" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDelete(site.domain)}
                        >
                          <Trash className="w-4 h-4 text-red-600" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </Card>
      <Dialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
        <DialogContent className="rounded-xl shadow-2xl p-8 max-w-md bg-white">
          <DialogHeader>
            <DialogTitle className="text-2xl font-bold mb-2">
              Are you sure?
            </DialogTitle>
            <DialogDescription className="text-gray-500 text-base mb-8">
              Once deleted, this site cannot be recovered. Please confirm your
              action.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex justify-end gap-3">
            <Button
              variant="outline"
              className="border border-gray-300 text-gray-800 font-medium rounded-md px-6 py-2"
              onClick={cancelDelete}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              className="bg-red-600 hover:bg-red-700 text-white font-medium rounded-md px-6 py-2"
              onClick={confirmDelete}
            >
              Yes, delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
