import sgMail from "@sendgrid/mail";

// SendGrid configuration
// Name: [Your SendGrid Name]
// ID: [Your SendGrid ID]
// Key: [Your SendGrid API Key]
sgMail.setApiKey(process.env.SENDGRID_API_KEY || "");

// Email templates
export const emailTemplates = {
  otpVerification: (otp: string, firstName: string) => ({
    subject: "Verify Your Email - Adds AI",
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .otp-code { background: #667eea; color: white; padding: 15px 30px; font-size: 24px; font-weight: bold; text-align: center; border-radius: 8px; margin: 20px 0; letter-spacing: 3px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
          .button { display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Email Verification</h1>
            <p>Welcome to Adds AI!</p>
          </div>
          <div class="content">
            <h2>Hello ${firstName}!</h2>
            <p>Thank you for signing up with Adds AI. To complete your registration, please verify your email address using the OTP code below:</p>
            
            <div class="otp-code">${otp}</div>
            
            <p><strong>This code will expire in 10 minutes.</strong></p>
            
            <p>If you didn't create an account with us, please ignore this email.</p>
            
            <p>Best regards,<br>The Adds AI Team</p>
          </div>
          <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      Hello ${firstName}!
      
      Thank you for signing up with Adds AI. To complete your registration, please verify your email address using this OTP code:
      
      ${otp}
      
      This code will expire in 10 minutes.
      
      If you didn't create an account with us, please ignore this email.
      
      Best regards,
      The Adds AI Team
    `,
  }),
};

// Send email function
export async function sendEmail(
  to: string,
  template: { subject: string; html: string; text: string }
) {
  try {
    const msg = {
      to,
      from: {
        email:
          process.env.SENDGRID_FROM_EMAIL ||
          process.env.SENDGRID_VERIFIED_SENDER ||
          "<EMAIL>",
        name: "Adds AI",
      },
      subject: template.subject,
      text: template.text,
      html: template.html,
    };

    const response = await sgMail.send(msg);
    console.log(
      "Email sent successfully via SendGrid:",
      response[0].statusCode
    );
    return { success: true, messageId: response[0].headers["x-message-id"] };
  } catch (error: any) {
    console.error("Error sending email via SendGrid:", error);
    return { success: false, error: error?.message || error?.toString() };
  }
}

// Send OTP email
export async function sendOTPEmail(
  email: string,
  otp: string,
  firstName: string
) {
  const template = emailTemplates.otpVerification(otp, firstName);
  return await sendEmail(email, template);
}
